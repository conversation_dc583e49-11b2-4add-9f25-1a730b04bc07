# 极简 RAG 聊天应用开发脚本

## 项目概述

**目标**：构建一个极简的 RAG（检索增强生成）聊天应用，用户可以将 TXT 文件放入指定目录，运行脚本后通过网页界面与文档内容进行对话。

**核心功能**：

- 📁 文档处理：自动索引指定目录下的所有 TXT 文件
- 🔍 混合检索：BM25 关键词检索 + 向量相似度检索
- 💬 智能问答：基于检索到的文档内容生成回答
- 🌐 Web 界面：简洁的单页面聊天界面
- 🚀 一键部署：运行脚本即可启动服务

**技术栈**：

- 后端：Python 3.11 + FastAPI + LlamaIndex + ChromaDB (SQLite)
- 前端：HTML + TailwindCSS + Vanilla JavaScript
- 数据库：SQLite（ChromaDB 底层存储）
- LLM：GPT-4o-mini（通过代理 API）
- 向量模型：text-embedding-3-small

## 开发步骤

### 第一步：环境准备

#### 1.1 创建项目目录结构

```bash
mkdir fast-gzmdrw-chat
cd fast-gzmdrw-chat

# 创建目录结构
mkdir -p {backend,frontend,data,storage}
mkdir -p backend/{app,config}
mkdir -p frontend/{static/{css,js},templates}
```

#### 1.2 创建 Python 虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate
```

#### 1.3 安装依赖包

创建 `requirements.txt`：

```txt
fastapi==0.115.13
uvicorn[standard]==0.34.3
llama-index==0.12.42
llama-index-vector-stores-chroma
llama-index-embeddings-openai
llama-index-llms-openai
chromadb==1.0.12
pydantic-settings==2.9.1
python-multipart
python-dotenv
```

安装依赖：

```bash
pip install -r requirements.txt
```

### 第二步：配置管理

#### 2.1 创建环境配置文件

创建 `.env` 文件：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://your-proxy-url.com/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB SQLite配置
CHROMA_DB_IMPL=duckdb+parquet
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://chat.example.org"]
```

#### 2.2 创建配置类

创建 `backend/config/settings.py`：

### 第三步：后端开发

#### 3.1 创建 RAG 服务核心类

创建 `backend/app/rag_service.py`：

#### 3.2 创建 FastAPI 应用

创建 `backend/app/main.py`：

#### 3.3 创建启动脚本

创建 `backend/run.py`：

### 第四步：前端开发

#### 4.1 创建聊天页面 HTML

#### 4.2 创建 JavaScript 交互逻辑

创建 `frontend/static/js/chat.js`：

### 第五步：部署和运行

#### 5.1 创建项目启动脚本

创建根目录下的 `start.py`：

#### 5.2 创建 README 文档

创建 `README.md`：

````markdown
# RAG 聊天应用

一个基于 LlamaIndex 和 ChromaDB 的极简 RAG（检索增强生成）聊天应用。

## 功能特点

- 🔍 **智能检索**: 结合 BM25 关键词检索和向量相似度检索
- 💬 **自然对话**: 基于 GPT-4o-mini 的智能问答
- 📁 **简单易用**: 只需将 TXT 文件放入 data 目录即可
- 🌐 **Web 界面**: 美观的单页面聊天界面
- ⚡ **快速部署**: 一键启动，无需复杂配置

## 快速开始

### 1. 环境要求

- Python 3.11+
- OpenAI API 密钥（支持代理）

### 2. 安装和配置

```bash
# 克隆或下载项目
git clone <your-repo-url>
cd rag-chat-app

# 创建.env配置文件
cp .env.example .env
# 编辑.env文件，填入你的OpenAI API配置

# 一键启动
python start.py
```
````

### 3. 使用方法

1. 将 TXT 文档放入 `data/` 目录
2. 访问 http://localhost:8000
3. 点击"重新加载文档"按钮
4. 开始与文档对话！

## 项目结构

```
rag-chat-app/
├── backend/           # 后端代码
│   ├── app/          # FastAPI应用
│   ├── config/       # 配置管理
│   └── run.py        # 启动脚本
├── frontend/         # 前端代码
│   ├── static/       # 静态资源
│   └── chat.html     # 聊天页面
├── data/            # 文档目录（放置TXT文件）
├── storage/         # ChromaDB数据存储目录
│   ├── chroma.sqlite3    # SQLite数据库文件（自动生成）
│   └── [向量数据文件]     # 向量嵌入数据（自动生成）
├── .env             # 环境配置
├── requirements.txt # 依赖包
└── start.py         # 一键启动脚本
```

## API 接口

- `GET /` - 聊天页面
- `GET /api/status` - 系统状态
- `POST /api/load-documents` - 加载文档
- `POST /api/query` - 查询问答

## 技术栈

- **后端**: FastAPI + LlamaIndex + ChromaDB
- **数据库**: SQLite（ChromaDB 底层存储，自动管理）
- **前端**: HTML + TailwindCSS + Vanilla JS
- **LLM**: GPT-4o-mini
- **向量模型**: text-embedding-3-small

### 数据存储说明

本应用使用 ChromaDB 作为向量数据库，ChromaDB 底层使用 SQLite 进行数据持久化：

- **chroma.sqlite3**: 存储集合元数据、文档信息
- **向量数据文件**: 存储文档的向量嵌入
- **自动管理**: 无需手动操作 SQLite，ChromaDB 自动处理所有数据库操作

## 常见问题

**Q: 如何添加新文档？**
A: 将 TXT 文件放入 data 目录，然后点击"重新加载文档"按钮。

**Q: 支持哪些文档格式？**
A: 目前只支持 TXT 格式，后续可扩展支持 PDF、Word 等。

**Q: 如何修改模型配置？**
A: 编辑.env 文件中的 OPENAI_MODEL 和 EMBEDDING_MODEL 参数。

**Q: 数据存储在哪里？**
A: 向量数据存储在 `storage/` 目录下的 SQLite 数据库中，包括 `chroma.sqlite3` 等文件。

**Q: 如何清空数据库重新开始？**
A: 删除 `storage/` 目录下的所有文件，重启应用即可自动重建数据库。

**Q: 数据库文件很大怎么办？**
A: ChromaDB 会自动压缩数据，如需手动清理可删除不需要的文档后重新加载。

#### 6.2 性能优化建议

1. **向量数据库优化**：

   - 调整 chunk_size 和 chunk_overlap 参数
   - 使用更高效的嵌入模型
   - 实现增量索引更新

2. **SQLite 数据库优化**：

   - 定期清理无用的向量数据
   - 监控 `storage/` 目录大小
   - 考虑使用 `VACUUM` 命令压缩 SQLite 数据库
   - 备份重要的 `chroma.sqlite3` 文件

3. **检索优化**：

   - 实现混合检索（BM25 + 向量检索）
   - 添加重排序机制
   - 优化相似度阈值

4. **用户体验优化**：
   - 添加流式输出支持
   - 实现对话历史记录
   - 添加文档预览功能

## 总结

通过以上步骤，您可以快速构建一个功能完整的 RAG 聊天应用。这个应用具有以下优势：

1. **简单易用**：一键启动，无需复杂配置
2. **功能完整**：包含文档加载、向量检索、智能问答等核心功能
3. **易于扩展**：模块化设计，便于添加新功能
4. **生产就绪**：包含错误处理、日志记录等生产环境必需功能

您可以根据实际需求对应用进行定制和扩展，比如支持更多文档格式、添加用户认证、实现多轮对话等功能。
